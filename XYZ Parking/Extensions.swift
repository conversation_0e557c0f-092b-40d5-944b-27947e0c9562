//
//  Extensions.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation

extension String {
    /// 将字符串中每个单词的首字母大写
    var titleCased: String {
        return self.capitalized
    }
    
    /// 自定义的首字母大写方法，处理特殊情况
    var properTitleCase: String {
        return self.lowercased()
            .split(separator: " ")
            .map { word in
                guard !word.isEmpty else { return "" }
                return word.prefix(1).uppercased() + word.dropFirst().lowercased()
            }
            .joined(separator: " ")
    }
}
