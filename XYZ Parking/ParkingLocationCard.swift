//
//  ParkingLocationCard.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import CoreLocation

struct ParkingLocationCard: View {
    let parkingLocation: ParkingLocation
    let userLocation: CLLocation?
    let isTopCard: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Top section with title and distance
            HStack(alignment: .top, spacing: 12) {
                // Parking icon
                Image(systemName: "car.fill")
                    .font(.title2)
                    .foregroundStyle(.blue)
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(.blue.opacity(0.1))
                    )

                // Main content
                VStack(alignment: .leading, spacing: 6) {
                    // Title and distance row
                    HStack(alignment: .firstTextBaseline) {
                        Text(parkingLocation.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundStyle(.primary)
                            .lineLimit(1)

                        Spacer()

                        // Distance badge
                        Text(parkingLocation.formattedDistance(from: userLocation))
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundStyle(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(.blue)
                            )
                    }

                    // Address and street info
                    VStack(alignment: .leading, spacing: 2) {
                        if let onStreet = parkingLocation.onStreet {
                            Text("On \(onStreet.titleCased)")
                                .font(.caption)
                                .foregroundStyle(.primary)
                                .fontWeight(.medium)
                        }

                        Text(parkingLocation.address.titleCased)
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                }

                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .padding(.top, 4)
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)

            // Bottom section with status and restrictions
            VStack(spacing: 8) {
                HStack(spacing: 16) {
                    // Status info
                    HStack(spacing: 4) {
                        Image(systemName: parkingLocation.isOccupied ? "car.fill" : "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundStyle(parkingLocation.isOccupied ? .red : .green)

                        Text(parkingLocation.statusText)
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundStyle(parkingLocation.isOccupied ? .red : .green)
                    }

                    Spacer()

                    // Zone ID
                    if let zoneId = parkingLocation.zoneId {
                        HStack(spacing: 4) {
                            Image(systemName: "location.circle.fill")
                                .font(.caption)
                                .foregroundStyle(.blue)

                            Text("Zone \(zoneId)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundStyle(.blue)
                        }
                    }
                }

                // Restriction info
                HStack(spacing: 16) {
                    // Restriction display
                    if let restrictionDisplay = parkingLocation.restrictionDisplay {
                        HStack(spacing: 4) {
                            Image(systemName: "clock.fill")
                                .font(.caption2)
                                .foregroundStyle(.orange)

                            Text(restrictionDisplay)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundStyle(.orange)
                        }
                    }

                    Spacer()

                    // Max stay time
                    Text(parkingLocation.maxStayText)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
            .padding(.top, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.thinMaterial)
                .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(.quaternary, lineWidth: 0.5)
        )
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        ParkingLocationCard(
            parkingLocation: ParkingLocation(
                id: "51614",
                kerbsideId: "51614",
                name: "Parking Spot 51614",
                address: "Collins Street between Spring Street and Exhibition Street",
                price: "$0",
                spots: "1",
                latitude: -37.8136,
                longitude: 144.9631,
                zoneId: "7303",
                onStreet: "Collins Street",
                isOccupied: false,
                lastUpdated: "2025-03-25T11:09:20.000Z",
                restrictionStartTime: "07:30:00",
                restrictionFinishTime: "18:30:00",
                maxStayMinutes: 120,
                restrictionDisplay: "2P",
                restrictionDay: "mon-fri"
            ),
            userLocation: CLLocation(latitude: -37.8140, longitude: 144.9633),
            isTopCard: true,
            onTap: {}
        )

        ParkingLocationCard(
            parkingLocation: ParkingLocation(
                id: "17954",
                kerbsideId: "17954",
                name: "Parking Spot 17954",
                address: "Bourke Street between Queen Street and King Street",
                price: "$0",
                spots: "1",
                latitude: -37.8140,
                longitude: 144.9633,
                zoneId: "7265",
                onStreet: "Bourke Street",
                isOccupied: true,
                lastUpdated: "2025-03-25T10:56:53.000Z",
                restrictionStartTime: "07:30:00",
                restrictionFinishTime: "18:30:00",
                maxStayMinutes: 60,
                restrictionDisplay: "1P",
                restrictionDay: "mon-sat"
            ),
            userLocation: nil,
            isTopCard: false,
            onTap: {}
        )
    }
    .padding()
    .background(Color(.systemGray6))
}
