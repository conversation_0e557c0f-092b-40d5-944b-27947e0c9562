# XYZ Parking API Server

REST API server for XYZ Parking iOS app to connect with MySQL database.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Make sure MySQL is running in Docker:
```bash
cd ../database_source_code
sh ./start_db.sh
./import_data.sh
```

3. Start the API server:
```bash
npm start
```

Or for development with auto-reload:
```bash
npm run dev
```

## API Endpoints

### GET /api/parking-spots
Get all parking spots or filter by location.

Query parameters:
- `lat` (optional): Latitude for location filtering
- `lng` (optional): Longitude for location filtering  
- `radius` (optional): Search radius (default: 0.01)

Example:
```
GET /api/parking-spots?lat=-37.8136&lng=144.9631&radius=0.02
```

### GET /api/parking-spots/:id
Get specific parking spot details by kerbside_id.

Example:
```
GET /api/parking-spots/51614
```

### GET /api/health
Health check endpoint.

## Response Format

```json
{
  "id": "51614",
  "kerbsideId": "51614", 
  "name": "Parking Spot 51614",
  "address": "Road segment description",
  "latitude": -37.81620493158199,
  "longitude": 144.96978894261684,
  "zoneId": "7303",
  "onStreet": "Collins St",
  "isOccupied": false,
  "lastUpdated": "2025-03-25T11:09:20.000Z",
  "restrictionStartTime": "07:30:00",
  "restrictionFinishTime": "18:30:00", 
  "maxStayMinutes": 120,
  "restrictionDisplay": "2P",
  "restrictionDay": "mon-fri",
  "price": "$0",
  "spots": "1"
}
```
