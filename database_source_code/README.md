1. Start xyz parking db: `sh ./start_db.sh`
2. Wait few minutes for db startup, then import data to database: `chmod +x import_data.sh && ./import_data.sh`
3. Test if db works as expectation:
    - Login to mysql container: `docker exec -it xyz-parking-db-container mysql -uroot -proot`
    - Run sql commands to make sure the tables created such as:cle
        ```
        USE xyz_parking_db;
        SHOW TABLES;
        SELECT * FROM PARKING_BAY;
        ```
4. Remove mysql container (if not use any more):
`docker rm -f xyz-parking-db-container`
