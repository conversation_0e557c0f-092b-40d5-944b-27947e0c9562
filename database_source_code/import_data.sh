#!/bin/bash
set -e

# set container name
CONTAINER_NAME="xyz-parking-db-container"

# set database credentials
XYZ_DB_NAME="xyz_parking_db"
XYZ_DB_USER="root"
XYZ_DB_PASS="root"

head -n 1 data/data4_clean.csv | cut -d',' -f1 > data/data_zoneid.csv
tail -n +2 data/data4_clean.csv | cut -d',' -f1 | sort | uniq >> data/data_zoneid.csv

# Remove lines with empty value on kerbside_id from data_zoneid.csv
awk -F',' 'NR==1 || ($2 != "" && $2 != "\"\"")' "data/data2_clean.csv" > "data/data2_clean2.csv"

docker exec -i $CONTAINER_NAME mysql -u$XYZ_DB_USER -p$XYZ_DB_PASS $XYZ_DB_NAME < init.sql


# copy data files into the container
docker cp data/data_zoneid.csv $CONTAINER_NAME:/var/lib/mysql-files/
docker cp data/data1_clean.csv $CONTAINER_NAME:/var/lib/mysql-files/
docker cp data/data2_clean2.csv $CONTAINER_NAME:/var/lib/mysql-files/
docker cp data/data3_clean.csv $CONTAINER_NAME:/var/lib/mysql-files/
docker cp data/data4_clean.csv $CONTAINER_NAME:/var/lib/mysql-files/

# exucute the import sql script
docker exec -i $CONTAINER_NAME mysql -u$XYZ_DB_USER -p$XYZ_DB_PASS $XYZ_DB_NAME < import_data.sql
rm -fr data/data_zoneid.csv data/data2_clean2.csv

echo "Data import finished!"
