
-- insert initial data into PARKING_ZONE
SELECT 'Inserting initial data into PARKING_ZONE' AS message;
LOAD DATA INFILE '/var/lib/mysql-files/data_zoneid.csv'
INTO TABLE PARKING_ZONE
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(zone_id);

-- load data into PARKING_BAY_SENSOR
SELECT 'Loading data into PARKING_BAY_SENSOR' AS message;
LOAD DATA INFILE '/var/lib/mysql-files/data1_clean.csv'
INTO TABLE PARKING_BAY_SENSOR
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(@notinclude, status_timestamp, zone_id, @notinclude, kerbside_id, @notinclude, latitude, longitude, status_sign);

-- load data into PAR<PERSON>ING_BAY
SELECT 'Loading data into PARKING_BAY' AS message;
LOAD DATA INFILE '/var/lib/mysql-files/data2_clean2.csv'
INTO TABLE PARKING_BAY
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(road_segment_id, kerbside_id, road_segment_description, latitude, longitude, @notinclude, @notinclude);


-- load data into PARKING_ZONE_SEG
SELECT 'Loading data into PARKING_ZONE_SEG' AS message;
LOAD DATA INFILE '/var/lib/mysql-files/data3_clean.csv'
INTO TABLE PARKING_ZONE_SEG
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(zone_id, on_street, street_from, street_to, street_segment_id);

-- load data into PARKING_SIGN_PLATE
SELECT 'Loading data into PARKING_SIGN_PLATE' AS message;
LOAD DATA INFILE '/var/lib/mysql-files/data4_clean.csv'
INTO TABLE PARKING_SIGN_PLATE
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(zone_id, restriction_day, restriction_start_time, restriction_finish_time, restriction_display, duration_minutes, duration_hours, @max_stay_minutes)
SET 
 max_stay_minutes = IF(@max_stay_minutes = '' OR @max_stay_minutes IS NULL, NULL, @max_stay_minutes);