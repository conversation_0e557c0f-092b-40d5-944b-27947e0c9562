
-- create database parking_db
CREATE DATABASE IF NOT EXISTS xyz_parking_db;
USE xyz_parking_db;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS PARKING_BAY_SENSOR;
DROP TABLE IF EXISTS PARKING_BAY;
DROP TABLE IF EXISTS PARKING_ZONE_SEG;
DROP TABLE IF EXISTS PARKING_SIGN_PLATE;
DROP TABLE IF EXISTS PARKING_ZONE;

-- create table PARKING_ZONE 
CREATE TABLE PARKING_ZONE (
    zone_id VARCHAR(10) PRIMARY KEY
);

-- create table PARKING_BAY_SENSOR 
CREATE TABLE PARKING_BAY_SENSOR (
    kerbside_id VARCHAR(10) PRIMARY KEY,
    zone_id VARCHAR(10) NULL,
    status_sign BOOLEAN,
    latitude DOUBLE,
    longitude DOUBLE,
    status_timestamp TIMESTAMP
);

-- create table PARKING_BAY 
CREATE TABLE PARKING_BAY (
    kerbside_id VARCHAR(10),
    road_segment_id VARCHAR(10),
    road_segment_description VARCHAR(120),
    latitude DOUBLE,
    longitude DOUBLE,
    PRIMARY KEY (kerbside_id, road_segment_id)
);

-- create table PARKING_ZONE_SEG 
CREATE TABLE PARKING_ZONE_SEG (
    zone_id VARCHAR(10),
    street_segment_id VARCHAR(10),
    on_street VARCHAR(30),
    street_from VARCHAR(30),
    street_to VARCHAR(30),
    PRIMARY KEY (zone_id, street_segment_id)
);

-- create table PARKING_SIGN_PLATE 
CREATE TABLE PARKING_SIGN_PLATE (
    sign_id INT AUTO_INCREMENT PRIMARY KEY,
    zone_id VARCHAR(10),
    restriction_day VARCHAR(10),
    restriction_start_time TIME,
    restriction_finish_time TIME,
    restriction_display VARCHAR(6),
    duration_minutes INT DEFAULT 0,
    duration_hours INT DEFAULT 0,
    max_stay_minutes INT DEFAULT 0,
    FOREIGN KEY (zone_id) REFERENCES PARKING_ZONE(zone_id)
);
